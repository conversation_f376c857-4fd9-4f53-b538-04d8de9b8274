#ifndef COLOR_MATCHING_APP_H
#define COLOR_MATCHING_APP_H

#include <Arduino.h>
#include <Adafruit_NeoPixel.h>
#include "TFTDisplay.h"
#include "ColorSensor.h"
#include "ButtonHandler.h"
#include <BLEDevice.h>
#include "FS.h"
#include "LittleFS.h"

class ColorMatchingApp {
public:
    ColorMatchingApp();
    bool begin();
    void update();

private:
    // Components
    TFTDisplay display;
    ColorSensor colorSensor;
    Adafruit_NeoPixel rgbLed;

    // State management
    enum AppState {
        STATE_LIVE_READING,
        STATE_CALIBRATING,
        STATE_BLE_SETUP,
        STATE_SENDING_DATA,
        STATE_SAVING_DATA
    };
    AppState currentState;

    // BLE
    void setupBLE();
    void handleBLE();
    BLEServer* pServer;
    BLECharacteristic* pCharacteristic;
    bool bleConnected;

    // Storage
    void saveReading();
    void loadCalibration();
    void saveCalibration();

    // Helper functions
    void processColorData();
    void updateDisplay();
    void handleUserInput();
};

#endif // COLOR_MATCHING_APP_H
