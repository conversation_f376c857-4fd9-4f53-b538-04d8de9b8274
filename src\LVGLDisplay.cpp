#include "LVGLDisplay.h"

// Static member initialization
bool LVGLDisplay::buttonPressed = false;
int16_t LVGLDisplay::lastX = 0;
int16_t LVGLDisplay::lastY = 0;

LVGLDisplay::LVGLDisplay() : 
    tft(TFT_CS, TFT_DC, TFT_RST),
    display(nullptr),
    inputDevice(nullptr),
    displayBuffer1(nullptr),
    displayBuffer2(nullptr)
{
}

bool LVGLDisplay::begin() {
    // Initialize display hardware
    initializeDisplay();
    
    // Initialize LVGL
    initializeLVGL();
    
    // Setup display driver
    setupDisplayDriver();
    
    // Setup input driver
    setupInputDriver();
    
    Serial.println("LVGL Display initialized successfully");
    return true;
}

void LVGLDisplay::initializeDisplay() {
    // Initialize SPI display
    tft.init(SCREEN_WIDTH, SCREEN_HEIGHT, SPI_MODE0);
    tft.setRotation(SCREEN_ROTATION);
    
    // Set backlight pin
    pinMode(TFT_BL, OUTPUT);
    setBrightness(255); // Full brightness
    
    // Clear screen
    tft.fillScreen(ST77XX_BLACK);
    
    Serial.println("ST7789 Display initialized");
}

void LVGLDisplay::initializeLVGL() {
    // Initialize LVGL
    lv_init();
    
    // Allocate display buffers
    displayBuffer1 = (lv_color_t*)heap_caps_malloc(BUFFER_SIZE * sizeof(lv_color_t), MALLOC_CAP_DMA);
    displayBuffer2 = (lv_color_t*)heap_caps_malloc(BUFFER_SIZE * sizeof(lv_color_t), MALLOC_CAP_DMA);
    
    if (!displayBuffer1 || !displayBuffer2) {
        Serial.println("Failed to allocate LVGL display buffers");
        return;
    }
    
    // Initialize display buffer
    lv_disp_draw_buf_init(&drawBuffer, displayBuffer1, displayBuffer2, BUFFER_SIZE);
    
    Serial.println("LVGL core initialized");
}

void LVGLDisplay::setupDisplayDriver() {
    // Initialize display driver
    lv_disp_drv_init(&displayDriver);

    // Set display driver parameters
    displayDriver.hor_res = SCREEN_WIDTH;
    displayDriver.ver_res = SCREEN_HEIGHT;
    displayDriver.flush_cb = displayFlush;
    displayDriver.draw_buf = &drawBuffer;
    displayDriver.user_data = this;  // Store instance pointer for callbacks

    // Register display driver
    display = lv_disp_drv_register(&displayDriver);

    Serial.println("LVGL display driver registered");
}

void LVGLDisplay::setupInputDriver() {
    // Initialize input driver
    lv_indev_drv_init(&inputDriver);
    
    // Set input driver parameters
    inputDriver.type = LV_INDEV_TYPE_POINTER;
    inputDriver.read_cb = touchpadRead;
    
    // Register input driver
    inputDevice = lv_indev_drv_register(&inputDriver);
    
    Serial.println("LVGL input driver registered");
}

void LVGLDisplay::update() {
    // Handle LVGL tasks
    lv_timer_handler();
}

void LVGLDisplay::setBrightness(uint8_t brightness) {
    analogWrite(TFT_BL, brightness);
}

void LVGLDisplay::displayFlush(lv_disp_drv_t* disp, const lv_area_t* area, lv_color_t* color_p) {
    // Get the TFT instance from the display driver user data
    LVGLDisplay* display_instance = (LVGLDisplay*)disp->user_data;

    if (!display_instance) {
        lv_disp_flush_ready(disp);
        return;
    }

    uint32_t w = (area->x2 - area->x1 + 1);
    uint32_t h = (area->y2 - area->y1 + 1);

    // Set the drawing window
    display_instance->tft.setAddrWindow(area->x1, area->y1, w, h);

    // Write pixel data
    display_instance->tft.startWrite();
    display_instance->tft.writePixels((uint16_t*)color_p, w * h);
    display_instance->tft.endWrite();

    // Inform LVGL that flushing is done
    lv_disp_flush_ready(disp);
}

void LVGLDisplay::touchpadRead(lv_indev_drv_t* indev_driver, lv_indev_data_t* data) {
    // Read button states and simulate touch
    int16_t x, y;
    bool pressed = false;
    
    // Check if any button is pressed and map to screen coordinates
    if (!digitalRead(BUTTON_SELECT)) {
        pressed = true;
        x = SCREEN_WIDTH / 2;  // Center of screen
        y = SCREEN_HEIGHT / 2;
    } else if (!digitalRead(BUTTON_UP)) {
        pressed = true;
        x = SCREEN_WIDTH / 2;
        y = SCREEN_HEIGHT / 4;  // Upper part of screen
    } else if (!digitalRead(BUTTON_DOWN)) {
        pressed = true;
        x = SCREEN_WIDTH / 2;
        y = 3 * SCREEN_HEIGHT / 4;  // Lower part of screen
    }
    
    if (pressed) {
        data->state = LV_INDEV_STATE_PRESSED;
        data->point.x = x;
        data->point.y = y;
        lastX = x;
        lastY = y;
        buttonPressed = true;
    } else {
        data->state = LV_INDEV_STATE_RELEASED;
        data->point.x = lastX;
        data->point.y = lastY;
        buttonPressed = false;
    }
}

bool LVGLDisplay::readButtons(int16_t* x, int16_t* y) {
    // Check button states
    bool selectPressed = !digitalRead(BUTTON_SELECT);
    bool upPressed = !digitalRead(BUTTON_UP);
    bool downPressed = !digitalRead(BUTTON_DOWN);
    
    if (selectPressed || upPressed || downPressed) {
        if (selectPressed) {
            *x = SCREEN_WIDTH / 2;
            *y = SCREEN_HEIGHT / 2;
        } else if (upPressed) {
            *x = SCREEN_WIDTH / 2;
            *y = SCREEN_HEIGHT / 4;
        } else if (downPressed) {
            *x = SCREEN_WIDTH / 2;
            *y = 3 * SCREEN_HEIGHT / 4;
        }
        return true;
    }
    
    return false;
}
