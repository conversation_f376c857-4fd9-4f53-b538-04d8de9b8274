[env:esp32s3]
platform = espressif32
board = esp32-s3-devkitc-1
framework = arduino
monitor_speed = 115200
upload_speed = 115200
upload_port = COM6
monitor_port = COM6
upload_flags =
    --before=default_reset
    --after=hard_reset
    --chip=esp32s3
    --no-stub
build_flags =
    -DCORE_DEBUG_LEVEL=5
    -DARDUINO_USB_MODE=1
    -DARDUINO_USB_CDC_ON_BOOT=1
    -I${PROJECT_DIR}/src
    -I${PROJECT_DIR}
    -DBOARD_HAS_PSRAM
build_unflags = -std=gnu++11
build_src_flags = -std=gnu++17
lib_deps =
    dfrobot/DFRobot_TCS3430
    bblanchon/Ard<PERSON><PERSON><PERSON><PERSON>@^7.0.0
    me-no-dev/ESPAsyncWebServer@^1.2.3
    me-no-dev/AsyncTCP@^1.1.1
lib_ldf_mode = deep
