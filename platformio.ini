[env:esp32s3]
platform = espressif32
board = esp32-s3-devkitc-1
framework = arduino
monitor_speed = 115200
upload_speed = 115200
upload_port = COM4
monitor_port = COM4
upload_flags =
    --before=default_reset
    --after=hard_reset
    --chip=esp32s3
    --no-stub
build_flags =
    -DCORE_DEBUG_LEVEL=5
    -DARDUINO_USB_MODE=1
    -DARDUINO_USB_CDC_ON_BOOT=1
    -I${PROJECT_DIR}/src
    -I${PROJECT_DIR}
    -DLV_CONF_INCLUDE_SIMPLE
    -DLV_CONF_PATH="${PROJECT_DIR}/lv_conf.h"
    ; TFT_eSPI pin definitions for ESP32-S3-Touch-LCD-1.69
    -DUSER_SETUP_LOADED=1
    -DST7789_DRIVER=1
    -DTFT_WIDTH=240
    -DTFT_HEIGHT=280
    -DTFT_MOSI=35
    -DTFT_SCLK=36
    -DTFT_CS=5
    -DTFT_DC=6
    -DTFT_RST=4
    -DTFT_BL=38
    -DLOAD_GLCD=1
    -DLOAD_FONT2=1
    -DLOAD_FONT4=1
    -DLOAD_FONT6=1
    -DLOAD_FONT7=1
    -DLOAD_FONT8=1
    -DLOAD_GFXFF=1
    -DSMOOTH_FONT=1
    -DSPI_FREQUENCY=40000000
    -DSPI_READ_FREQUENCY=20000000
build_unflags = -std=gnu++11
build_src_flags = -std=gnu++17
lib_deps =
    adafruit/Adafruit GFX Library
    adafruit/Adafruit BusIO
    adafruit/Adafruit ST7735 and ST7789 Library
    dfrobot/DFRobot_TCS3430
    Wire
    adafruit/Adafruit NeoPixel
    bodmer/TFT_eSPI
    lvgl/lvgl@^8.3.0
    https://github.com/lewisxhe/SensorLib.git
    moononournation/GFX Library for Arduino
lib_ldf_mode = deep
