#ifndef HARDWARE_CONFIG_H
#define HARDWARE_CONFIG_H

// ESP32-S3-Touch-LCD-1.69 Pin Definitions (NEW VERSION)
// Updated pin mappings for the new hardware revision

// Power Management Pins
#define BUZZER_PIN      42  // Buzzer (was GPIO33)
#define RTC_INT_PIN     39  // RTC interrupt (was GPIO41)
#define SYS_EN_PIN      41  // Power control enable (was GPIO35)
#define SYS_OUT_PIN     40  // Power button/control output (was GPIO36)

// Display Pins (ST7789 via SPI)
#define TFT_CS          5   // Chip select
#define TFT_DC          6   // Data/Command
#define TFT_RST         4   // Reset
#define TFT_MOSI        35  // SPI MOSI
#define TFT_SCLK        36  // SPI Clock
#define TFT_BL          38  // Backlight control

// I2C Pins for Sensors
#define SDA_PIN         8   // I2C Data
#define SCL_PIN         9   // I2C Clock

// Sensor Interrupt Pins
#define COLOR_SENSOR_INT_PIN  15  // TCS3430 interrupt
#define IMU_INT_PIN          16   // QMI8658 interrupt

// RGB LED
#define RGB_LED_PIN     21  // NeoPixel LED

// Button Pins (if using external buttons)
#define BUTTON_SELECT   17
#define BUTTON_UP       18
#define BUTTON_DOWN     19

// Display Configuration
#define SCREEN_WIDTH    240
#define SCREEN_HEIGHT   280
#define SCREEN_ROTATION 0

// I2C Device Addresses
#define TCS3430_I2C_ADDR    0x39  // Color sensor
#define QMI8658_I2C_ADDR    0x6B  // IMU sensor
#define PCF85063_I2C_ADDR   0x51  // RTC

// Power Management Configuration
#define POWER_ON_DELAY_MS       100   // Delay after power on
#define POWER_OFF_DELAY_MS      1000  // Delay before power off
#define BUTTON_DEBOUNCE_MS      50    // Button debounce time
#define LONG_PRESS_TIME_MS      2000  // Long press threshold
#define DOUBLE_CLICK_TIME_MS    500   // Double click threshold
#define BUZZER_BEEP_DURATION_MS 200   // Buzzer beep duration

// LVGL Configuration
#define LV_HOR_RES_MAX          SCREEN_WIDTH
#define LV_VER_RES_MAX          SCREEN_HEIGHT
#define LV_COLOR_DEPTH          16
#define LV_TICK_PERIOD_MS       5

// Sensor Update Intervals
#define COLOR_SENSOR_UPDATE_MS  100   // Color sensor reading interval
#define IMU_UPDATE_MS          50    // IMU reading interval
#define RTC_UPDATE_MS          1000  // RTC reading interval
#define DISPLAY_UPDATE_MS      50    // Display refresh interval

#endif // HARDWARE_CONFIG_H
