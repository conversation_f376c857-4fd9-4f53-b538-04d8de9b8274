#include "ColorMatchingUI.h"

ColorMatchingUI::ColorMatchingUI() :
    mainScreen(nullptr),
    menuScreen(nullptr),
    currentState(STATE_MAIN)
{
}

bool ColorMatchingUI::begin() {
    // Initialize styles
    lv_style_init(&colorPanelStyle);
    lv_style_set_radius(&colorPanelStyle, 10);
    lv_style_set_border_width(&colorPanelStyle, 2);
    lv_style_set_border_color(&colorPanelStyle, lv_color_hex(0x333333));
    lv_style_set_pad_all(&colorPanelStyle, 10);
    
    lv_style_init(&dataPanelStyle);
    lv_style_set_radius(&dataPanelStyle, 5);
    lv_style_set_border_width(&dataPanelStyle, 1);
    lv_style_set_border_color(&dataPanelStyle, lv_color_hex(0x666666));
    lv_style_set_pad_all(&dataPanelStyle, 5);
    
    lv_style_init(&buttonStyle);
    lv_style_set_radius(&buttonStyle, 5);
    lv_style_set_bg_color(&buttonStyle, lv_color_hex(0x2196F3));
    lv_style_set_text_color(&buttonStyle, lv_color_white());
    
    lv_style_init(&titleStyle);
    lv_style_set_text_font(&titleStyle, &lv_font_montserrat_16);
    lv_style_set_text_color(&titleStyle, lv_color_hex(0x333333));
    
    // Create screens
    createMainScreen();
    createMenuScreen();
    
    // Load main screen
    switchToScreen(mainScreen);
    
    Serial.println("Color Matching UI initialized");
    return true;
}

void ColorMatchingUI::createMainScreen() {
    mainScreen = lv_obj_create(NULL);
    lv_obj_set_style_bg_color(mainScreen, lv_color_hex(0xF5F5F5), 0);
    
    createColorPanel();
    createDataPanel();
    createStatusPanel();
    createButtons();
}

void ColorMatchingUI::createColorPanel() {
    // Color display panel
    colorPanel = lv_obj_create(mainScreen);
    lv_obj_add_style(colorPanel, &colorPanelStyle, 0);
    lv_obj_set_size(colorPanel, 220, 80);
    lv_obj_set_pos(colorPanel, 10, 10);
    
    // Color rectangle
    colorRect = lv_obj_create(colorPanel);
    lv_obj_set_size(colorRect, 60, 60);
    lv_obj_set_pos(colorRect, 10, 10);
    lv_obj_set_style_bg_color(colorRect, lv_color_hex(0x000000), 0);
    lv_obj_set_style_border_width(colorRect, 1, 0);
    
    // RGB label
    rgbLabel = lv_label_create(colorPanel);
    lv_label_set_text(rgbLabel, "RGB: 0,0,0");
    lv_obj_set_pos(rgbLabel, 80, 10);
    lv_obj_set_style_text_font(rgbLabel, &lv_font_montserrat_12, 0);
    
    // Hex label
    hexLabel = lv_label_create(colorPanel);
    lv_label_set_text(hexLabel, "HEX: #000000");
    lv_obj_set_pos(hexLabel, 80, 30);
    lv_obj_set_style_text_font(hexLabel, &lv_font_montserrat_12, 0);
}

void ColorMatchingUI::createDataPanel() {
    // Data display panel
    dataPanel = lv_obj_create(mainScreen);
    lv_obj_add_style(dataPanel, &dataPanelStyle, 0);
    lv_obj_set_size(dataPanel, 220, 100);
    lv_obj_set_pos(dataPanel, 10, 100);
    
    // XYZ values
    xyzLabel = lv_label_create(dataPanel);
    lv_label_set_text(xyzLabel, "XYZ: 0.0, 0.0, 0.0");
    lv_obj_set_pos(xyzLabel, 5, 5);
    lv_obj_set_style_text_font(xyzLabel, &lv_font_montserrat_10, 0);
    
    // xy chromaticity
    xyLabel = lv_label_create(dataPanel);
    lv_label_set_text(xyLabel, "xy: 0.000, 0.000");
    lv_obj_set_pos(xyLabel, 5, 25);
    lv_obj_set_style_text_font(xyLabel, &lv_font_montserrat_10, 0);
    
    // Luminance
    luminanceLabel = lv_label_create(dataPanel);
    lv_label_set_text(luminanceLabel, "Luminance: 0.0");
    lv_obj_set_pos(luminanceLabel, 5, 45);
    lv_obj_set_style_text_font(luminanceLabel, &lv_font_montserrat_10, 0);
    
    // Color name
    colorNameLabel = lv_label_create(dataPanel);
    lv_label_set_text(colorNameLabel, "Color: Unknown");
    lv_obj_set_pos(colorNameLabel, 5, 65);
    lv_obj_set_style_text_font(colorNameLabel, &lv_font_montserrat_10, 0);
    
    // Delta E
    deltaELabel = lv_label_create(dataPanel);
    lv_label_set_text(deltaELabel, "ΔE: 0.0");
    lv_obj_set_pos(deltaELabel, 120, 65);
    lv_obj_set_style_text_font(deltaELabel, &lv_font_montserrat_10, 0);
}

void ColorMatchingUI::createStatusPanel() {
    // Status panel
    statusPanel = lv_obj_create(mainScreen);
    lv_obj_add_style(statusPanel, &dataPanelStyle, 0);
    lv_obj_set_size(statusPanel, 220, 40);
    lv_obj_set_pos(statusPanel, 10, 210);
    
    // Status label
    statusLabel = lv_label_create(statusPanel);
    lv_label_set_text(statusLabel, "Status: Ready");
    lv_obj_set_pos(statusLabel, 5, 5);
    lv_obj_set_style_text_font(statusLabel, &lv_font_montserrat_10, 0);
    
    // Message label
    messageLabel = lv_label_create(statusPanel);
    lv_label_set_text(messageLabel, "");
    lv_obj_set_pos(messageLabel, 5, 20);
    lv_obj_set_style_text_font(messageLabel, &lv_font_montserrat_10, 0);
}

void ColorMatchingUI::createButtons() {
    // Calibrate button
    calibrateBtn = lv_btn_create(mainScreen);
    lv_obj_add_style(calibrateBtn, &buttonStyle, 0);
    lv_obj_set_size(calibrateBtn, 70, 30);
    lv_obj_set_pos(calibrateBtn, 10, 260);
    lv_obj_add_event_cb(calibrateBtn, buttonEventHandler, LV_EVENT_CLICKED, this);
    
    lv_obj_t* calibrateLabel = lv_label_create(calibrateBtn);
    lv_label_set_text(calibrateLabel, "Calibrate");
    lv_obj_center(calibrateLabel);
    
    // Save button
    saveBtn = lv_btn_create(mainScreen);
    lv_obj_add_style(saveBtn, &buttonStyle, 0);
    lv_obj_set_size(saveBtn, 70, 30);
    lv_obj_set_pos(saveBtn, 85, 260);
    lv_obj_add_event_cb(saveBtn, buttonEventHandler, LV_EVENT_CLICKED, this);
    
    lv_obj_t* saveLabel = lv_label_create(saveBtn);
    lv_label_set_text(saveLabel, "Save");
    lv_obj_center(saveLabel);
    
    // Menu button
    menuBtn = lv_btn_create(mainScreen);
    lv_obj_add_style(menuBtn, &buttonStyle, 0);
    lv_obj_set_size(menuBtn, 70, 30);
    lv_obj_set_pos(menuBtn, 160, 260);
    lv_obj_add_event_cb(menuBtn, buttonEventHandler, LV_EVENT_CLICKED, this);
    
    lv_obj_t* menuLabel = lv_label_create(menuBtn);
    lv_label_set_text(menuLabel, "Menu");
    lv_obj_center(menuLabel);
}

void ColorMatchingUI::createMenuScreen() {
    menuScreen = lv_obj_create(NULL);
    lv_obj_set_style_bg_color(menuScreen, lv_color_hex(0xF5F5F5), 0);
    
    // Title
    lv_obj_t* title = lv_label_create(menuScreen);
    lv_label_set_text(title, "Color Matcher Menu");
    lv_obj_add_style(title, &titleStyle, 0);
    lv_obj_set_pos(title, 10, 10);
    
    // Menu list
    menuList = lv_list_create(menuScreen);
    lv_obj_set_size(menuList, 220, 200);
    lv_obj_set_pos(menuList, 10, 40);
    
    // Add menu items
    lv_obj_t* btn1 = lv_list_add_btn(menuList, LV_SYMBOL_SETTINGS, "Settings");
    lv_obj_add_event_cb(btn1, menuEventHandler, LV_EVENT_CLICKED, this);
    
    lv_obj_t* btn2 = lv_list_add_btn(menuList, LV_SYMBOL_WIFI, "Bluetooth");
    lv_obj_add_event_cb(btn2, menuEventHandler, LV_EVENT_CLICKED, this);
    
    lv_obj_t* btn3 = lv_list_add_btn(menuList, LV_SYMBOL_FILE, "Data Export");
    lv_obj_add_event_cb(btn3, menuEventHandler, LV_EVENT_CLICKED, this);
    
    lv_obj_t* btn4 = lv_list_add_btn(menuList, LV_SYMBOL_HOME, "Back to Main");
    lv_obj_add_event_cb(btn4, menuEventHandler, LV_EVENT_CLICKED, this);
}

void ColorMatchingUI::update() {
    // Handle any UI updates if needed
}

void ColorMatchingUI::updateColorData(const ColorSensor::ColorData& data) {
    updateColorDisplay(data.r, data.g, data.b, data.hex);
    updateDataDisplay(data.X, data.Y, data.Z, data.x, data.y, data.Y_luminance);
    updateColorInfo(data.closestName, data.deltaE, data.isVividWhite);
}

void ColorMatchingUI::updateColorDisplay(uint8_t r, uint8_t g, uint8_t b, const char* hex) {
    // Update color rectangle
    lv_color_t color = lv_color_make(r, g, b);
    lv_obj_set_style_bg_color(colorRect, color, 0);
    
    // Update RGB label
    char rgbText[32];
    snprintf(rgbText, sizeof(rgbText), "RGB: %d,%d,%d", r, g, b);
    lv_label_set_text(rgbLabel, rgbText);
    
    // Update hex label
    char hexText[32];
    snprintf(hexText, sizeof(hexText), "HEX: %s", hex);
    lv_label_set_text(hexLabel, hexText);
}

void ColorMatchingUI::updateDataDisplay(float X, float Y, float Z, float x, float y, float luminance) {
    char text[64];
    
    // Update XYZ
    snprintf(text, sizeof(text), "XYZ: %.1f, %.1f, %.1f", X, Y, Z);
    lv_label_set_text(xyzLabel, text);
    
    // Update xy
    snprintf(text, sizeof(text), "xy: %.3f, %.3f", x, y);
    lv_label_set_text(xyLabel, text);
    
    // Update luminance
    snprintf(text, sizeof(text), "Luminance: %.1f", luminance);
    lv_label_set_text(luminanceLabel, text);
}

void ColorMatchingUI::updateColorInfo(const char* colorName, float deltaE, bool isVividWhite) {
    char text[64];
    
    // Update color name
    snprintf(text, sizeof(text), "Color: %s", colorName);
    lv_label_set_text(colorNameLabel, text);
    
    // Update delta E
    snprintf(text, sizeof(text), "ΔE: %.1f", deltaE);
    lv_label_set_text(deltaELabel, text);
    
    // Update status based on vivid white detection
    if (isVividWhite) {
        lv_label_set_text(statusLabel, "Status: Vivid White");
    } else {
        lv_label_set_text(statusLabel, "Status: Color Match");
    }
}

void ColorMatchingUI::showMessage(const char* message) {
    lv_label_set_text(messageLabel, message);
}

void ColorMatchingUI::showError(const char* error) {
    char errorText[64];
    snprintf(errorText, sizeof(errorText), "Error: %s", error);
    lv_label_set_text(messageLabel, errorText);
    lv_obj_set_style_text_color(messageLabel, lv_color_hex(0xFF0000), 0);
}

void ColorMatchingUI::switchToScreen(lv_obj_t* screen) {
    lv_scr_load(screen);
}

void ColorMatchingUI::buttonEventHandler(lv_event_t* e) {
    ColorMatchingUI* ui = (ColorMatchingUI*)lv_event_get_user_data(e);
    lv_obj_t* btn = lv_event_get_target(e);
    
    if (btn == ui->calibrateBtn) {
        ui->showMessage("Calibrating...");
        // Trigger calibration
    } else if (btn == ui->saveBtn) {
        ui->showMessage("Saving data...");
        // Trigger save
    } else if (btn == ui->menuBtn) {
        ui->switchToScreen(ui->menuScreen);
        ui->currentState = STATE_MENU;
    }
}

void ColorMatchingUI::menuEventHandler(lv_event_t* e) {
    ColorMatchingUI* ui = (ColorMatchingUI*)lv_event_get_user_data(e);
    lv_obj_t* btn = lv_event_get_target(e);
    
    // Get button text to determine action
    lv_obj_t* label = lv_obj_get_child(btn, 0);
    const char* text = lv_label_get_text(label);
    
    if (strcmp(text, "Back to Main") == 0) {
        ui->switchToScreen(ui->mainScreen);
        ui->currentState = STATE_MAIN;
    } else {
        ui->showMessage("Feature not implemented yet");
        ui->switchToScreen(ui->mainScreen);
        ui->currentState = STATE_MAIN;
    }
}
