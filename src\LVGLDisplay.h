#ifndef LVGL_DISPLAY_H
#define LVGL_DISPLAY_H

#include <Arduino.h>
#include <lvgl.h>
#include <Adafruit_GFX.h>
#include <Adafruit_ST7789.h>
#include "HardwareConfig.h"

class LVGLDisplay {
public:
    LVGLDisplay();
    bool begin();
    void update();
    void setBrightness(uint8_t brightness);
    
    // LVGL callbacks
    static void displayFlush(lv_disp_drv_t* disp, const lv_area_t* area, lv_color_t* color_p);
    static void touchpadRead(lv_indev_drv_t* indev_driver, lv_indev_data_t* data);
    
    // Get the display instance for LVGL
    lv_disp_t* getDisplay() { return display; }
    
private:
    Adafruit_ST7789 tft;
    lv_disp_t* display;
    lv_disp_drv_t displayDriver;
    lv_indev_drv_t inputDriver;
    lv_indev_t* inputDevice;
    
    // Display buffers for LVGL
    static const size_t BUFFER_SIZE = SCREEN_WIDTH * SCREEN_HEIGHT / 10;
    lv_color_t* displayBuffer1;
    lv_color_t* displayBuffer2;
    lv_disp_draw_buf_t drawBuffer;
    
    // Button state tracking
    static bool buttonPressed;
    static int16_t lastX, lastY;
    
    void initializeDisplay();
    void initializeLVGL();
    void setupDisplayDriver();
    void setupInputDriver();
    bool readButtons(int16_t* x, int16_t* y);
};

#endif // LVGL_DISPLAY_H
